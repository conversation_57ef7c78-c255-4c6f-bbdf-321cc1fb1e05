#!/usr/bin/env python3
"""
简单的阿里云文档解析测试脚本
测试基本连接性和API调用
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import Settings
from src.document_rag.ali_service import AlibabaCloudService
from src.services.minio_service import MinioService


async def test_alibaba_connection():
    """测试阿里云连接"""
    print("🔗 测试阿里云文档解析API连接")
    print("=" * 50)
    
    try:
        # 初始化服务
        settings = Settings()
        ali_service = AlibabaCloudService(settings.alibaba_cloud)
        minio_service = MinioService(settings.minio)
        
        print(f"✅ 服务初始化成功")
        print(f"   - 阿里云端点: {settings.alibaba_cloud.ENDPOINT}")
        print(f"   - Access Key ID: {settings.alibaba_cloud.ACCESS_KEY_ID[:10]}...")
        
        # 查找测试文件
        test_dir = Path("src/docs/测试文档")
        test_files = list(test_dir.glob("*.pdf"))
        
        if not test_files:
            print("❌ 未找到测试文件")
            return
            
        test_file = str(test_files[0])
        file_name = os.path.basename(test_file)
        
        print(f"📁 使用测试文件: {file_name}")
        
        # 上传文件到MinIO
        print(f"\n📤 上传文件到MinIO...")
        
        with open(test_file, 'rb') as f:
            file_content = f.read()
            
        minio_url = await minio_service.upload_file(
            bucket_name=settings.document_api.BUCKET_NAME,
            object_name=f"simple_test/{file_name}",
            file_stream=file_content,
            length=len(file_content),
            content_type="application/pdf"
        )
        
        print(f"✅ MinIO上传成功: {minio_url}")
        
        # 测试阿里云API调用
        print(f"\n🚀 测试阿里云API调用...")
        
        # 设置较长的超时时间
        start_time = time.time()
        
        try:
            submit_result = await asyncio.wait_for(
                ali_service.submit_file(
                    file_url=minio_url,
                    file_name=file_name
                ),
                timeout=60.0  # 60秒超时
            )
            
            elapsed_time = time.time() - start_time
            
            print(f"📋 API调用结果 (耗时: {elapsed_time:.2f}s):")
            print(json.dumps(submit_result, indent=2, ensure_ascii=False))
            
            if submit_result["success"]:
                job_id = submit_result["job_id"]
                print(f"✅ 任务提交成功，Job ID: {job_id}")
                
                # 测试状态查询
                print(f"\n🔍 测试状态查询...")
                
                status_result = await asyncio.wait_for(
                    ali_service.check_job_status(job_id),
                    timeout=30.0
                )
                
                print(f"📊 状态查询结果:")
                print(json.dumps(status_result, indent=2, ensure_ascii=False))
                
                # 保存测试结果
                test_result = {
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "file_name": file_name,
                    "minio_url": minio_url,
                    "submit_result": submit_result,
                    "status_result": status_result,
                    "api_call_time": elapsed_time
                }
                
                result_file = f"simple_test_result_{int(time.time())}.json"
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(test_result, f, indent=2, ensure_ascii=False)
                    
                print(f"\n📄 测试结果已保存到: {result_file}")
                
            else:
                print(f"❌ 任务提交失败: {submit_result.get('error')}")
                
        except asyncio.TimeoutError:
            elapsed_time = time.time() - start_time
            print(f"⏰ API调用超时 (耗时: {elapsed_time:.2f}s)")
            print("   这可能是网络问题或阿里云服务响应慢")
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"❌ API调用异常 (耗时: {elapsed_time:.2f}s): {e}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_network_connectivity():
    """测试网络连通性"""
    print("\n🌐 测试网络连通性")
    print("=" * 30)
    
    import aiohttp
    
    # 测试阿里云端点连通性
    endpoint = "https://docmind-api.cn-hangzhou.aliyuncs.com"
    
    try:
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            print(f"🔗 测试连接: {endpoint}")
            
            start_time = time.time()
            async with session.get(endpoint) as response:
                elapsed_time = time.time() - start_time
                
                print(f"✅ 连接成功 (耗时: {elapsed_time:.2f}s)")
                print(f"   - 状态码: {response.status}")
                print(f"   - 响应头: {dict(response.headers)}")
                
    except asyncio.TimeoutError:
        print(f"⏰ 连接超时")
    except Exception as e:
        print(f"❌ 连接失败: {e}")


async def analyze_existing_results():
    """分析已有的测试结果"""
    print("\n📊 分析已有测试结果")
    print("=" * 30)
    
    # 查找已有的结果文件
    result_files = list(Path(".").glob("*test_result*.json"))
    
    if not result_files:
        print("📝 未找到已有的测试结果文件")
        return
        
    print(f"📁 找到 {len(result_files)} 个结果文件:")
    
    for result_file in result_files:
        print(f"\n📄 分析文件: {result_file}")
        
        try:
            with open(result_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            print(f"   - 时间戳: {data.get('timestamp', 'N/A')}")
            
            if 'submit_result' in data:
                submit_result = data['submit_result']
                print(f"   - 提交成功: {submit_result.get('success', False)}")
                if submit_result.get('success'):
                    print(f"   - Job ID: {submit_result.get('job_id', 'N/A')}")
                else:
                    print(f"   - 错误: {submit_result.get('error', 'N/A')}")
                    
            if 'status_result' in data:
                status_result = data['status_result']
                print(f"   - 状态查询成功: {status_result.get('success', False)}")
                if status_result.get('success'):
                    print(f"   - 任务状态: {status_result.get('status', 'N/A')}")
                    
            if 'api_call_time' in data:
                print(f"   - API调用耗时: {data['api_call_time']:.2f}s")
                
        except Exception as e:
            print(f"   ❌ 解析失败: {e}")


async def main():
    """主函数"""
    print("🧪 简单阿里云文档解析测试")
    print("=" * 60)
    
    # 1. 测试网络连通性
    await test_network_connectivity()
    
    # 2. 测试阿里云API
    await test_alibaba_connection()
    
    # 3. 分析已有结果
    await analyze_existing_results()
    
    print(f"\n🎉 测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
