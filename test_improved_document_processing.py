#!/usr/bin/env python3
"""
测试改进后的文档处理器
验证基于阿里云测试结果的业务逻辑改进
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import Settings
from src.document_rag.document_processor import DocumentProcessor
from src.services.minio_service import MinioService
from src.document_rag.ali_service import AlibabaCloudService
from src.services.dify_service import DifyService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_content_analysis():
    """测试内容分析功能"""
    print("\n🔍 测试内容分析功能")
    print("=" * 50)
    
    # 初始化处理器
    settings = Settings()
    minio_service = MinioService(settings.minio)
    ali_service = AlibabaCloudService(settings.alibaba_cloud)
    dify_service = DifyService(settings.dify)
    
    processor = DocumentProcessor(
        minio_service=minio_service,
        alibaba_service=ali_service,
        dify_service=dify_service
    )
    
    # 测试不同类型的内容
    test_cases = [
        {
            "name": "纯文本内容",
            "content": "这是一个包含大量文本的文档。\n\n## 标题\n\n这里有更多的文本内容，用于测试文本提取功能。文档包含多个段落和丰富的文本信息。"
        },
        {
            "name": "纯图片内容",
            "content": "![image1](http://example.com/image1.jpg)\n![image2](http://example.com/image2.png)\n![image3](http://example.com/image3.gif)"
        },
        {
            "name": "混合内容",
            "content": "# 文档标题\n\n这是文档的介绍文本。\n\n![图片1](http://example.com/chart.png)\n\n## 数据分析\n\n这里是详细的分析内容。\n\n![图片2](http://example.com/graph.jpg)"
        },
        {
            "name": "空内容",
            "content": ""
        },
        {
            "name": "表格内容",
            "content": "# 数据报告\n\n| 项目 | 数值 | 备注 |\n|------|------|------|\n| A | 100 | 正常 |\n| B | 200 | 增长 |\n\n![图表](http://example.com/table.png)"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试案例 {i}: {test_case['name']}")
        
        analysis = await processor._analyze_document_content(test_case["content"])
        
        print(f"   - 内容类型: {analysis['content_type']}")
        print(f"   - 质量评分: {analysis['quality_score']}")
        print(f"   - 包含文本: {analysis['has_text']}")
        print(f"   - 包含图片: {analysis['has_images']}")
        print(f"   - 包含表格: {analysis['has_tables']}")
        print(f"   - 文本长度: {analysis['text_length']}")
        print(f"   - 图片数量: {analysis['image_count']}")
        print(f"   - 文本比例: {analysis['text_ratio']:.2f}")
        
        if analysis["recommendations"]:
            print(f"   - 建议: {'; '.join(analysis['recommendations'])}")
        
    print(f"\n✅ 内容分析功能测试完成")


async def test_real_document_processing():
    """测试真实文档处理"""
    print("\n📄 测试真实文档处理")
    print("=" * 50)
    
    # 初始化处理器
    settings = Settings()
    minio_service = MinioService(settings.minio)
    ali_service = AlibabaCloudService(settings.alibaba_cloud)
    dify_service = DifyService(settings.dify)
    
    processor = DocumentProcessor(
        minio_service=minio_service,
        alibaba_service=ali_service,
        dify_service=dify_service
    )
    
    # 使用之前测试成功的文件
    test_file_path = "src/docs/测试文档/20240429103255110730.pdf"
    
    if not Path(test_file_path).exists():
        print(f"❌ 测试文件不存在: {test_file_path}")
        return
        
    print(f"📁 使用测试文件: {test_file_path}")
    
    # 上传文件到MinIO
    with open(test_file_path, 'rb') as f:
        file_content = f.read()
        
    file_name = Path(test_file_path).name
    
    minio_url = await minio_service.upload_file(
        bucket_name=settings.document_api.BUCKET_NAME,
        object_name=f"improved_test/{file_name}",
        file_stream=file_content,
        length=len(file_content),
        content_type="application/pdf"
    )
    
    print(f"✅ 文件上传成功: {minio_url}")
    
    # 测试阿里云解析和内容分析
    print(f"\n🚀 开始阿里云解析...")
    
    start_time = time.time()
    
    # 提交解析任务
    submit_result = await ali_service.submit_file(
        file_url=minio_url,
        file_name=file_name
    )
    
    if not submit_result["success"]:
        print(f"❌ 任务提交失败: {submit_result['error']}")
        return
        
    job_id = submit_result["job_id"]
    print(f"✅ 任务提交成功，Job ID: {job_id}")
    
    # 等待任务完成
    max_wait = 300
    check_interval = 15
    waited = 0
    
    while waited < max_wait:
        await asyncio.sleep(check_interval)
        waited += check_interval
        
        status_result = await ali_service.check_job_status(job_id)
        
        if status_result["success"] and status_result["is_completed"]:
            if status_result["is_success"]:
                print(f"✅ 解析完成，耗时: {waited}s")
                break
            else:
                print(f"❌ 解析失败: {status_result.get('message')}")
                return
                
        print(f"⏳ 等待中... ({waited}s)")
        
    if waited >= max_wait:
        print(f"⏰ 解析超时")
        return
        
    # 获取解析结果
    result = await ali_service.get_job_result(job_id)
    
    if not result["success"]:
        print(f"❌ 获取结果失败: {result['error']}")
        return
        
    markdown_content = result["result"]
    print(f"📄 获取解析结果成功，内容长度: {len(markdown_content)}")
    
    # 进行内容分析
    print(f"\n🔍 进行内容分析...")
    
    analysis = await processor._analyze_document_content(markdown_content)
    
    print(f"📊 内容分析结果:")
    print(f"   - 内容类型: {analysis['content_type']}")
    print(f"   - 质量评分: {analysis['quality_score']}")
    print(f"   - 包含文本: {analysis['has_text']}")
    print(f"   - 包含图片: {analysis['has_images']}")
    print(f"   - 包含表格: {analysis['has_tables']}")
    print(f"   - 文本长度: {analysis['text_length']}")
    print(f"   - 图片数量: {analysis['image_count']}")
    print(f"   - 文本比例: {analysis['text_ratio']:.2f}")
    
    if analysis["recommendations"]:
        print(f"   - 建议:")
        for rec in analysis["recommendations"]:
            print(f"     • {rec}")
    
    # 保存分析结果
    analysis_result = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "file_name": file_name,
        "processing_time": waited,
        "content_length": len(markdown_content),
        "analysis": analysis,
        "raw_content_preview": markdown_content[:500] + "..." if len(markdown_content) > 500 else markdown_content
    }
    
    result_file = f"improved_processing_result_{int(time.time())}.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, indent=2, ensure_ascii=False)
        
    print(f"\n💾 分析结果已保存到: {result_file}")
    
    # 显示处理统计
    stats = processor.processing_stats
    print(f"\n📈 处理统计:")
    print(f"   - 总处理数: {stats['total_processed']}")
    print(f"   - 成功提取文本: {stats['successful_text_extraction']}")
    print(f"   - 仅图片文档: {stats['image_only_documents']}")
    print(f"   - 处理失败: {stats['failed_processing']}")
    
    total_time = time.time() - start_time
    print(f"\n🎉 测试完成，总耗时: {total_time:.2f}s")


async def main():
    """主函数"""
    print("🧪 改进的文档处理器测试")
    print("=" * 60)
    
    # 1. 测试内容分析功能
    await test_content_analysis()
    
    # 2. 测试真实文档处理
    await test_real_document_processing()
    
    print(f"\n🎉 所有测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
