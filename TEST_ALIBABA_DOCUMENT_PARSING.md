# 阿里云文档解析测试指南

本文档提供了完整的阿里云文档解析和图片上传OSS功能的测试方案。

## 📋 测试脚本说明

### 1. `check_test_config.py` - 配置检查脚本
检查所有必要的配置是否正确设置，包括：
- 环境文件(.env)是否存在
- 测试文档是否存在
- 依赖包是否安装
- 阿里云配置是否正确
- MinIO配置是否正确

### 2. `test_alibaba_api_only.py` - 阿里云API单独测试
专门测试阿里云文档解析API的基础功能，不涉及MinIO：
- 使用公开PDF文件测试API连接
- 测试任务提交、状态检查、结果获取
- 分析解析内容的质量

### 3. `test_alibaba_document_parsing.py` - 完整功能测试
测试完整的文档解析流程：
- MinIO连接和文件上传
- 阿里云文档解析
- 图片下载和上传到MinIO
- Markdown链接替换
- 端到端完整测试

## 🚀 使用步骤

### 步骤1: 检查配置
```bash
python check_test_config.py
```

确保所有配置检查都通过。如果有问题，请按照提示修复。

### 步骤2: 测试阿里云API
```bash
python test_alibaba_api_only.py
```

这个测试使用公开的PDF文件，验证阿里云API是否正常工作。

### 步骤3: 完整功能测试
```bash
python test_alibaba_document_parsing.py
```

使用项目中的测试文档进行完整的端到端测试。

## 📁 测试输出文件

### 配置检查输出
- 控制台输出：显示各项配置的检查结果

### API单独测试输出
- `alibaba_api_test_result.md` - 阿里云API解析的原始结果
- `alibaba_api_analysis.json` - 解析内容的分析结果

### 完整功能测试输出
- `test_original_result.md` - 阿里云解析的原始结果
- `test_processed_result.md` - 图片处理后的结果
- `test_analysis_result.json` - 详细的测试分析结果

## 🔧 配置要求

### 必需的环境变量
在`.env`文件中配置以下变量：

```bash
# 阿里云配置
ACCESS_KEY_ID=your_alibaba_access_key_id
ACCESS_KEY_SECRET=your_alibaba_access_key_secret
ENDPOINT=docmind-api.cn-hangzhou.aliyuncs.com

# MinIO配置
MINIO_ENDPOINT=your_minio_endpoint
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key
MINIO_SECURE=false

# 存储桶配置
BUCKET_NAME=zhanshu
```

### 测试文档
确保以下测试文档存在：
- `src/docs/测试文档/20240429103255110730.pdf`

## 🔍 问题诊断

### 问题1: 解析结果只有图片URL，没有文本
**可能原因：**
- 阿里云API配置问题
- 文档本身是图片为主的内容
- API参数设置不当

**诊断方法：**
1. 运行`test_alibaba_api_only.py`检查API基础功能
2. 查看`alibaba_api_analysis.json`中的`has_meaningful_content`字段
3. 检查`alibaba_api_test_result.md`的内容

### 问题2: 图片上传失败
**可能原因：**
- MinIO连接问题
- 网络问题
- 权限问题

**诊断方法：**
1. 运行`check_test_config.py`检查MinIO配置
2. 查看控制台的详细错误信息
3. 检查MinIO服务是否正常运行

### 问题3: 图片链接替换失败
**可能原因：**
- 阿里云临时URL过期
- 图片下载失败
- MinIO上传失败

**诊断方法：**
1. 查看`test_analysis_result.json`中的图片处理统计
2. 对比`test_original_result.md`和`test_processed_result.md`
3. 检查控制台的图片处理日志

## 📊 测试结果分析

### 正常结果指标
- `has_meaningful_content: true` - 有文本内容
- `text_lines > 0` - 文本行数大于0
- `image_count >= 0` - 图片数量（可以为0）
- 图片处理成功率 > 80%

### 异常结果指标
- `has_meaningful_content: false` - 没有文本内容
- `text_lines = 0` - 没有文本行
- 所有图片处理失败

## 🛠️ 常见修复方案

### 修复1: 阿里云API参数优化
如果解析结果质量不佳，可以尝试调整API参数：

```python
# 在 ali_service.py 的 submit_file 方法中
request = docmind_api20220711_models.SubmitDocParserJobRequest(
    file_url=file_url,
    file_name=file_name,
    formula_enhancement=True,  # 公式增强
    llm_enhancement=True,      # LLM增强
    # 可以尝试添加其他参数
)
```

### 修复2: 图片处理超时优化
如果图片处理经常超时，可以调整超时设置：

```python
# 在 settings.py 中增加超时时间
MINIO_WRITE_TIMEOUT: int = 600  # 增加到10分钟
```

### 修复3: 并发控制
如果遇到并发问题，可以减少并发数：

```python
# 在图片处理中使用信号量控制并发
semaphore = asyncio.Semaphore(3)  # 最多3个并发
```

## 📞 技术支持

如果测试过程中遇到问题：

1. 首先运行配置检查脚本确认配置正确
2. 查看详细的错误日志和输出文件
3. 对比正常和异常的测试结果
4. 根据问题类型应用相应的修复方案

## 🎯 测试目标

通过这些测试脚本，我们要确保：

1. ✅ 阿里云文档解析API正常工作
2. ✅ 能够解析出有意义的文本内容
3. ✅ 图片能够正确下载和上传到MinIO
4. ✅ Markdown中的图片链接能够正确替换
5. ✅ 整个流程稳定可靠，没有数据丢失

运行完所有测试后，如果所有指标都正常，说明阿里云文档解析和图片上传OSS功能工作正常。
