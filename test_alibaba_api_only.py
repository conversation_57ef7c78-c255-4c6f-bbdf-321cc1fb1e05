#!/usr/bin/env python3
"""
阿里云文档解析API单独测试脚本

专门测试阿里云文档解析API的基础功能，不涉及MinIO
用于排查阿里云API本身是否正常工作
"""

import asyncio
import os
import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import alibaba_cloud_settings
from src.document_rag.ali_service import AlibabaCloudService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AlibabaAPITester:
    """阿里云API测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.alibaba_service = AlibabaCloudService(alibaba_cloud_settings)
        # 使用一个公开的PDF文件URL进行测试
        self.test_file_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
        
    async def test_submit_job(self) -> str:
        """测试提交解析任务"""
        logger.info("🚀 测试提交阿里云文档解析任务...")
        logger.info(f"📄 测试文件URL: {self.test_file_url}")
        
        result = await self.alibaba_service.submit_file(
            file_url=self.test_file_url,
            file_name="test_dummy.pdf"
        )
        
        logger.info(f"📊 提交结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if not result["success"]:
            raise Exception(f"任务提交失败: {result['error']}")
        
        job_id = result["job_id"]
        logger.info(f"✅ 任务提交成功，Job ID: {job_id}")
        return job_id
    
    async def test_check_status(self, job_id: str) -> dict:
        """测试检查任务状态"""
        logger.info(f"📊 检查任务状态: {job_id}")
        
        result = await self.alibaba_service.check_job_status(job_id)
        
        logger.info(f"📊 状态检查结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if not result["success"]:
            raise Exception(f"状态检查失败: {result['error']}")
        
        return result
    
    async def test_get_result(self, job_id: str) -> str:
        """测试获取解析结果"""
        logger.info(f"📄 获取解析结果: {job_id}")
        
        result = await self.alibaba_service.get_job_result(job_id)
        
        logger.info(f"📊 结果获取状态: {result['success']}")
        if result["success"] and result["result"]:
            content_length = len(result["result"])
            logger.info(f"📊 解析内容长度: {content_length} 字符")
            
            # 保存结果到文件
            with open("alibaba_api_test_result.md", "w", encoding="utf-8") as f:
                f.write(result["result"])
            logger.info("💾 解析结果已保存到: alibaba_api_test_result.md")
            
            # 显示前500字符
            preview = result["result"][:500]
            logger.info(f"📄 内容预览:\n{preview}...")
            
            return result["result"]
        else:
            logger.error(f"❌ 获取结果失败: {result.get('error', 'Unknown error')}")
            return ""
    
    async def wait_and_get_result(self, job_id: str, max_wait_time: int = 300) -> str:
        """等待任务完成并获取结果"""
        logger.info(f"⏳ 等待任务完成并获取结果 (最大等待时间: {max_wait_time}秒)...")
        
        check_interval = 10  # 10秒检查一次
        waited_time = 0
        
        while waited_time < max_wait_time:
            # 检查任务状态
            status_result = await self.test_check_status(job_id)
            
            if status_result["is_completed"]:
                if status_result["is_success"]:
                    logger.info("✅ 任务完成成功，获取结果...")
                    return await self.test_get_result(job_id)
                else:
                    raise Exception(f"任务执行失败: {status_result.get('error', 'Unknown error')}")
            
            # 等待下次检查
            await asyncio.sleep(check_interval)
            waited_time += check_interval
            logger.info(f"⏱️  已等待 {waited_time}/{max_wait_time} 秒...")
        
        raise Exception(f"任务超时，等待时间超过 {max_wait_time} 秒")
    
    async def analyze_content(self, content: str):
        """分析解析内容"""
        logger.info("📈 分析解析内容...")
        
        if not content:
            logger.error("❌ 解析内容为空")
            return
        
        import re
        
        # 统计基本信息
        lines = content.split('\n')
        non_empty_lines = [line.strip() for line in lines if line.strip()]
        
        # 查找图片
        img_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        images = re.findall(img_pattern, content)
        
        # 查找标题
        title_pattern = r'^#+\s+(.+)$'
        titles = []
        for line in lines:
            match = re.match(title_pattern, line.strip())
            if match:
                titles.append(match.group(1))
        
        # 统计文本内容（排除图片行）
        text_lines = []
        for line in non_empty_lines:
            if not re.match(r'^\s*!\[', line):  # 不是图片行
                text_lines.append(line)
        
        analysis = {
            "total_length": len(content),
            "total_lines": len(lines),
            "non_empty_lines": len(non_empty_lines),
            "text_lines": len(text_lines),
            "image_count": len(images),
            "title_count": len(titles),
            "has_meaningful_content": len(text_lines) > 0
        }
        
        # 保存分析结果
        with open("alibaba_api_analysis.json", "w", encoding="utf-8") as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        # 打印分析结果
        logger.info("📊 内容分析结果:")
        logger.info(f"   总长度: {analysis['total_length']} 字符")
        logger.info(f"   总行数: {analysis['total_lines']}")
        logger.info(f"   非空行数: {analysis['non_empty_lines']}")
        logger.info(f"   文本行数: {analysis['text_lines']}")
        logger.info(f"   图片数量: {analysis['image_count']}")
        logger.info(f"   标题数量: {analysis['title_count']}")
        logger.info(f"   是否有意义内容: {analysis['has_meaningful_content']}")
        
        # 显示图片信息
        if images:
            logger.info("🖼️  发现的图片:")
            for i, (alt_text, img_url) in enumerate(images[:5]):  # 只显示前5个
                logger.info(f"   {i+1}. {alt_text} -> {img_url[:80]}...")
        
        # 显示标题信息
        if titles:
            logger.info("📝 发现的标题:")
            for i, title in enumerate(titles[:5]):  # 只显示前5个
                logger.info(f"   {i+1}. {title}")
        
        # 检查问题
        if not analysis['has_meaningful_content']:
            logger.warning("⚠️  警告: 解析结果似乎没有有意义的文本内容")
            if analysis['image_count'] > 0:
                logger.info("💡 提示: 发现图片但没有文本，可能是图片为主的文档")
        
        return analysis
    
    async def run_complete_test(self):
        """运行完整的API测试"""
        logger.info("🚀 开始阿里云文档解析API测试")
        
        try:
            # 1. 提交任务
            job_id = await self.test_submit_job()
            
            # 2. 等待完成并获取结果
            content = await self.wait_and_get_result(job_id)
            
            # 3. 分析内容
            analysis = await self.analyze_content(content)
            
            logger.info("🎉 API测试完成！")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ API测试失败: {e}")
            raise

async def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("🧪 阿里云文档解析API单独测试")
    logger.info("=" * 60)
    
    # 检查配置
    logger.info("🔧 检查阿里云配置...")
    if not alibaba_cloud_settings.ACCESS_KEY_ID:
        logger.error("❌ 阿里云ACCESS_KEY_ID未配置")
        return
    if not alibaba_cloud_settings.ACCESS_KEY_SECRET:
        logger.error("❌ 阿里云ACCESS_KEY_SECRET未配置")
        return
    
    logger.info("✅ 阿里云配置检查通过")
    logger.info(f"🔗 API端点: {alibaba_cloud_settings.ENDPOINT}")
    
    # 运行测试
    tester = AlibabaAPITester()
    try:
        analysis = await tester.run_complete_test()
        logger.info("🎯 测试总结:")
        logger.info(f"   测试文件: {tester.test_file_url}")
        logger.info(f"   结果文件: alibaba_api_test_result.md")
        logger.info(f"   分析文件: alibaba_api_analysis.json")
        
        if analysis and analysis.get('has_meaningful_content'):
            logger.info("✅ 阿里云API工作正常，解析出了有意义的内容")
        else:
            logger.warning("⚠️  阿里云API可能存在问题，没有解析出有意义的内容")
        
    except Exception as e:
        logger.error(f"💥 API测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
