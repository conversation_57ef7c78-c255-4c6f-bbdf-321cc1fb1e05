#!/usr/bin/env python3
"""
测试阿里云响应数据解析
验证修复后的代码能否正确提取响应数据
"""

import asyncio
import json
import sys
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import Settings
from src.document_rag.ali_service import AlibabaCloudService
from src.services.minio_service import MinioService


async def test_real_document_with_text():
    """测试包含真实文本内容的文档"""
    print("🧪 测试包含文本内容的文档解析")
    print("=" * 60)
    
    # 初始化服务
    settings = Settings()
    ali_service = AlibabaCloudService(settings.alibaba_cloud)
    minio_service = MinioService(settings.minio)
    
    # 查找测试文件
    test_dir = Path("src/docs/测试文档")
    test_files = list(test_dir.glob("*.pdf"))
    
    if not test_files:
        print("❌ 未找到测试文件")
        return
        
    test_file = test_files[0]
    print(f"📁 使用测试文件: {test_file}")
    
    # 上传文件到MinIO
    with open(test_file, 'rb') as f:
        file_content = f.read()
        
    file_name = test_file.name
    
    minio_url = await minio_service.upload_file(
        bucket_name=settings.document_api.BUCKET_NAME,
        object_name=f"response_test/{file_name}",
        file_stream=file_content,
        length=len(file_content),
        content_type="application/pdf"
    )
    
    print(f"✅ 文件上传成功: {minio_url}")
    
    # 提交解析任务
    print(f"\n🚀 提交阿里云解析任务...")
    
    submit_result = await ali_service.submit_file(
        file_url=minio_url,
        file_name=file_name
    )
    
    if not submit_result["success"]:
        print(f"❌ 任务提交失败: {submit_result['error']}")
        return
        
    job_id = submit_result["job_id"]
    print(f"✅ 任务提交成功，Job ID: {job_id}")
    
    # 等待任务完成
    print(f"\n⏳ 等待任务完成...")
    max_wait = 300
    check_interval = 15
    waited = 0
    
    while waited < max_wait:
        await asyncio.sleep(check_interval)
        waited += check_interval
        
        status_result = await ali_service.check_job_status(job_id)
        
        print(f"📊 状态检查 ({waited}s): {status_result.get('status', 'unknown')}")
        
        if status_result["success"] and status_result["is_completed"]:
            if status_result["is_success"]:
                print(f"✅ 解析完成，耗时: {waited}s")
                break
            else:
                print(f"❌ 解析失败: {status_result.get('message')}")
                return
                
    if waited >= max_wait:
        print(f"⏰ 解析超时")
        return
        
    # 获取解析结果
    print(f"\n📥 获取解析结果...")
    
    result = await ali_service.get_job_result(job_id)
    
    print(f"📊 解析结果:")
    print(f"   - 成功: {result['success']}")
    print(f"   - 消息: {result['message']}")
    
    if result["success"]:
        content = result["result"]
        print(f"   - 内容长度: {len(content)} 字符")
        
        # 分析内容
        lines = content.split('\n')
        non_empty_lines = [line.strip() for line in lines if line.strip()]
        
        print(f"   - 总行数: {len(lines)}")
        print(f"   - 非空行数: {len(non_empty_lines)}")
        
        # 检查是否包含图片
        import re
        image_pattern = r'!\[.*?\]\(.*?\)'
        images = re.findall(image_pattern, content)
        print(f"   - 图片数量: {len(images)}")
        
        # 检查是否包含表格
        has_tables = '|' in content and '---' in content
        print(f"   - 包含表格: {has_tables}")
        
        # 显示内容预览
        print(f"\n📄 内容预览 (前500字符):")
        print("-" * 50)
        print(content[:500])
        if len(content) > 500:
            print("...")
        print("-" * 50)
        
        # 保存完整结果
        output_file = f"response_test_result_{int(time.time())}.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"💾 完整结果已保存到: {output_file}")
        
        # 分析内容类型
        text_content = re.sub(image_pattern, '', content).strip()
        text_length = len(text_content)
        
        if text_length > 50 and len(images) > 0:
            content_type = "mixed"
        elif text_length > 50:
            content_type = "text_only"
        elif len(images) > 0:
            content_type = "image_only"
        else:
            content_type = "empty"
            
        print(f"\n🔍 内容分析:")
        print(f"   - 内容类型: {content_type}")
        print(f"   - 文本长度: {text_length}")
        print(f"   - 图片数量: {len(images)}")
        print(f"   - 文本比例: {text_length / len(content) if content else 0:.2f}")
        
        # 生成分析报告
        analysis_report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "file_name": file_name,
            "job_id": job_id,
            "processing_time": waited,
            "content_stats": {
                "total_length": len(content),
                "text_length": text_length,
                "total_lines": len(lines),
                "non_empty_lines": len(non_empty_lines),
                "image_count": len(images),
                "has_tables": has_tables,
                "content_type": content_type
            },
            "content_preview": content[:200] + "..." if len(content) > 200 else content
        }
        
        report_file = f"response_analysis_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_report, f, indent=2, ensure_ascii=False)
        print(f"📊 分析报告已保存到: {report_file}")
        
    else:
        print(f"❌ 获取结果失败: {result.get('error')}")


async def test_multiple_documents():
    """测试多个文档"""
    print("\n🔄 批量测试多个文档")
    print("=" * 40)
    
    # 查找所有测试文件
    test_dir = Path("src/docs/测试文档")
    test_files = list(test_dir.glob("*.pdf"))
    
    if len(test_files) <= 1:
        print("📝 只有一个测试文件，跳过批量测试")
        return
        
    print(f"📁 找到 {len(test_files)} 个测试文件")
    
    # 初始化服务
    settings = Settings()
    ali_service = AlibabaCloudService(settings.alibaba_cloud)
    minio_service = MinioService(settings.minio)
    
    batch_results = []
    
    for i, test_file in enumerate(test_files[:2], 1):  # 限制测试2个文件
        print(f"\n📋 测试文件 {i}: {test_file.name}")
        
        try:
            # 上传文件
            with open(test_file, 'rb') as f:
                file_content = f.read()
                
            minio_url = await minio_service.upload_file(
                bucket_name=settings.document_api.BUCKET_NAME,
                object_name=f"batch_test/{test_file.name}",
                file_stream=file_content,
                length=len(file_content),
                content_type="application/pdf"
            )
            
            # 提交任务
            submit_result = await ali_service.submit_file(
                file_url=minio_url,
                file_name=test_file.name
            )
            
            if submit_result["success"]:
                job_id = submit_result["job_id"]
                print(f"   ✅ 任务提交成功: {job_id}")
                
                batch_results.append({
                    "file_name": test_file.name,
                    "job_id": job_id,
                    "status": "submitted"
                })
            else:
                print(f"   ❌ 任务提交失败: {submit_result['error']}")
                batch_results.append({
                    "file_name": test_file.name,
                    "status": "failed",
                    "error": submit_result['error']
                })
                
        except Exception as e:
            print(f"   ❌ 处理异常: {e}")
            batch_results.append({
                "file_name": test_file.name,
                "status": "error",
                "error": str(e)
            })
    
    # 保存批量测试结果
    batch_report = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "total_files": len(test_files),
        "tested_files": len(batch_results),
        "results": batch_results
    }
    
    batch_file = f"batch_test_report_{int(time.time())}.json"
    with open(batch_file, 'w', encoding='utf-8') as f:
        json.dump(batch_report, f, indent=2, ensure_ascii=False)
    print(f"\n📊 批量测试报告已保存到: {batch_file}")


async def main():
    """主函数"""
    print("🧪 阿里云响应数据解析测试")
    print("=" * 60)
    
    # 1. 测试真实文档解析
    await test_real_document_with_text()
    
    # 2. 批量测试（可选）
    # await test_multiple_documents()
    
    print(f"\n🎉 测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
