#!/usr/bin/env python3
"""
阿里云文档解析和图片上传OSS完整测试脚本

测试功能：
1. 阿里云文档解析API调用
2. 解析结果获取
3. 图片下载和上传到MinIO
4. Markdown链接替换
5. 完整的端到端测试

使用方法：
python test_alibaba_document_parsing.py
"""

import asyncio
import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import (
    alibaba_cloud_settings,
    minio_settings,
    document_api_settings
)
from src.document_rag.ali_service import AlibabaCloudService
from src.services.minio_service import MinioService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AlibabaDocumentParsingTester:
    """阿里云文档解析测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.alibaba_service = AlibabaCloudService(alibaba_cloud_settings)
        self.minio_service = MinioService(minio_settings)
        self.bucket_name = document_api_settings.BUCKET_NAME
        self.test_document_path = "src/docs/测试文档/20240429103255110730.pdf"
        
    async def test_minio_connection(self) -> bool:
        """测试MinIO连接"""
        logger.info("🔗 测试MinIO连接...")
        try:
            # 检查存储桶是否存在
            bucket_exists = await self.minio_service._check_bucket_exists(self.bucket_name)
            if not bucket_exists:
                logger.info(f"📦 创建存储桶: {self.bucket_name}")
                await self.minio_service._create_bucket_with_policy(self.bucket_name)
            
            logger.info("✅ MinIO连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ MinIO连接失败: {e}")
            return False
    
    async def upload_test_document(self) -> str:
        """上传测试文档到MinIO"""
        logger.info("📤 上传测试文档到MinIO...")
        
        if not os.path.exists(self.test_document_path):
            raise FileNotFoundError(f"测试文档不存在: {self.test_document_path}")
        
        # 读取文件
        with open(self.test_document_path, 'rb') as f:
            file_content = f.read()
        
        # 生成对象名称
        object_name = f"docs/test_document_{int(asyncio.get_event_loop().time())}.pdf"
        
        # 上传文件
        file_url = await self.minio_service.upload_file(
            bucket_name=self.bucket_name,
            object_name=object_name,
            file_stream=file_content,
            length=len(file_content),
            content_type="application/pdf"
        )
        
        logger.info(f"✅ 文档上传成功: {file_url}")
        return file_url
    
    async def test_alibaba_submit_job(self, file_url: str) -> str:
        """测试阿里云文档解析任务提交"""
        logger.info("🚀 提交阿里云文档解析任务...")
        
        result = await self.alibaba_service.submit_file(
            file_url=file_url,
            file_name="test_document.pdf"
        )
        
        if not result["success"]:
            raise Exception(f"任务提交失败: {result['error']}")
        
        job_id = result["job_id"]
        logger.info(f"✅ 任务提交成功，Job ID: {job_id}")
        return job_id
    
    async def wait_for_job_completion(self, job_id: str, max_wait_time: int = 300) -> Dict[str, Any]:
        """等待任务完成"""
        logger.info(f"⏳ 等待任务完成 (最大等待时间: {max_wait_time}秒)...")
        
        check_interval = 10  # 10秒检查一次
        waited_time = 0
        
        while waited_time < max_wait_time:
            # 检查任务状态
            status_result = await self.alibaba_service.check_job_status(job_id)
            
            if not status_result["success"]:
                raise Exception(f"检查任务状态失败: {status_result['error']}")
            
            logger.info(f"📊 任务状态: {status_result.get('status', 'Unknown')}")
            
            if status_result["is_completed"]:
                if status_result["is_success"]:
                    logger.info("✅ 任务完成成功")
                    return status_result
                else:
                    raise Exception(f"任务执行失败: {status_result.get('error', 'Unknown error')}")
            
            # 等待下次检查
            await asyncio.sleep(check_interval)
            waited_time += check_interval
            logger.info(f"⏱️  已等待 {waited_time}/{max_wait_time} 秒...")
        
        raise Exception(f"任务超时，等待时间超过 {max_wait_time} 秒")
    
    async def test_get_job_result(self, job_id: str) -> str:
        """测试获取解析结果"""
        logger.info("📄 获取解析结果...")
        
        result = await self.alibaba_service.get_job_result(job_id)
        
        if not result["success"]:
            raise Exception(f"获取结果失败: {result['error']}")
        
        markdown_content = result["result"]
        logger.info(f"✅ 获取解析结果成功，内容长度: {len(markdown_content)} 字符")
        
        # 保存原始结果到文件
        with open("test_original_result.md", "w", encoding="utf-8") as f:
            f.write(markdown_content)
        logger.info("💾 原始解析结果已保存到: test_original_result.md")
        
        return markdown_content
    
    async def test_image_processing(self, markdown_content: str) -> str:
        """测试图片处理和链接替换"""
        logger.info("🖼️  测试图片处理和链接替换...")
        
        # 统计原始图片数量
        import re
        img_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        original_images = re.findall(img_pattern, markdown_content)
        logger.info(f"📊 发现 {len(original_images)} 个图片链接")
        
        if len(original_images) == 0:
            logger.warning("⚠️  没有发现图片链接，跳过图片处理测试")
            return markdown_content
        
        # 显示前几个图片链接
        for i, (alt_text, img_url) in enumerate(original_images[:3]):
            logger.info(f"🔗 图片 {i+1}: {alt_text} -> {img_url[:100]}...")
        
        # 处理图片
        processed_content = await self.alibaba_service.download_images_from_markdown(
            markdown_content=markdown_content,
            minio_service=self.minio_service,
            original_filename="test_document.pdf",
            bucket_name=self.bucket_name
        )
        
        # 统计处理后的图片数量
        processed_images = re.findall(img_pattern, processed_content)
        logger.info(f"📊 处理后图片数量: {len(processed_images)}")
        
        # 保存处理后的结果
        with open("test_processed_result.md", "w", encoding="utf-8") as f:
            f.write(processed_content)
        logger.info("💾 处理后的结果已保存到: test_processed_result.md")
        
        return processed_content
    
    async def analyze_results(self, original_content: str, processed_content: str):
        """分析测试结果"""
        logger.info("📈 分析测试结果...")
        
        import re
        img_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        
        original_images = re.findall(img_pattern, original_content)
        processed_images = re.findall(img_pattern, processed_content)
        
        # 统计文本内容
        original_text_lines = [line.strip() for line in original_content.split('\n') if line.strip() and not line.strip().startswith('![')]
        processed_text_lines = [line.strip() for line in processed_content.split('\n') if line.strip() and not line.strip().startswith('![')]
        
        analysis = {
            "original_stats": {
                "total_length": len(original_content),
                "image_count": len(original_images),
                "text_lines": len(original_text_lines),
                "has_text_content": len(original_text_lines) > 0
            },
            "processed_stats": {
                "total_length": len(processed_content),
                "image_count": len(processed_images),
                "text_lines": len(processed_text_lines),
                "has_text_content": len(processed_text_lines) > 0
            },
            "image_processing": {
                "original_image_count": len(original_images),
                "processed_image_count": len(processed_images),
                "images_processed_successfully": len(processed_images) > 0
            }
        }
        
        # 保存分析结果
        with open("test_analysis_result.json", "w", encoding="utf-8") as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        # 打印分析结果
        logger.info("📊 测试结果分析:")
        logger.info(f"   原始内容长度: {analysis['original_stats']['total_length']} 字符")
        logger.info(f"   原始图片数量: {analysis['original_stats']['image_count']}")
        logger.info(f"   原始文本行数: {analysis['original_stats']['text_lines']}")
        logger.info(f"   是否包含文本: {analysis['original_stats']['has_text_content']}")
        logger.info(f"   处理后内容长度: {analysis['processed_stats']['total_length']} 字符")
        logger.info(f"   处理后图片数量: {analysis['processed_stats']['image_count']}")
        logger.info(f"   处理后文本行数: {analysis['processed_stats']['text_lines']}")
        logger.info(f"   是否包含文本: {analysis['processed_stats']['has_text_content']}")
        
        # 检查问题
        issues = []
        if not analysis['original_stats']['has_text_content']:
            issues.append("❌ 原始解析结果没有文本内容")
        if not analysis['processed_stats']['has_text_content']:
            issues.append("❌ 处理后结果没有文本内容")
        if analysis['original_stats']['image_count'] > 0 and analysis['processed_stats']['image_count'] == 0:
            issues.append("❌ 图片处理失败，所有图片链接丢失")
        
        if issues:
            logger.error("🚨 发现问题:")
            for issue in issues:
                logger.error(f"   {issue}")
        else:
            logger.info("✅ 所有测试通过，没有发现问题")
        
        return analysis
    
    async def run_complete_test(self):
        """运行完整测试"""
        logger.info("🚀 开始阿里云文档解析完整测试")
        
        try:
            # 1. 测试MinIO连接
            if not await self.test_minio_connection():
                raise Exception("MinIO连接测试失败")
            
            # 2. 上传测试文档
            file_url = await self.upload_test_document()
            
            # 3. 提交解析任务
            job_id = await self.test_alibaba_submit_job(file_url)
            
            # 4. 等待任务完成
            await self.wait_for_job_completion(job_id)
            
            # 5. 获取解析结果
            original_content = await self.test_get_job_result(job_id)
            
            # 6. 测试图片处理
            processed_content = await self.test_image_processing(original_content)
            
            # 7. 分析结果
            analysis = await self.analyze_results(original_content, processed_content)
            
            logger.info("🎉 完整测试完成！")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            raise

async def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("🧪 阿里云文档解析和图片上传OSS测试脚本")
    logger.info("=" * 60)
    
    # 检查配置
    logger.info("🔧 检查配置...")
    if not alibaba_cloud_settings.ACCESS_KEY_ID:
        logger.error("❌ 阿里云ACCESS_KEY_ID未配置")
        return
    if not alibaba_cloud_settings.ACCESS_KEY_SECRET:
        logger.error("❌ 阿里云ACCESS_KEY_SECRET未配置")
        return
    if not minio_settings.MINIO_ENDPOINT:
        logger.error("❌ MinIO配置未完整")
        return
    
    logger.info("✅ 配置检查通过")
    
    # 运行测试
    tester = AlibabaDocumentParsingTester()
    try:
        analysis = await tester.run_complete_test()
        logger.info("🎯 测试总结:")
        logger.info(f"   测试文档: {tester.test_document_path}")
        logger.info(f"   存储桶: {tester.bucket_name}")
        logger.info(f"   结果文件: test_original_result.md, test_processed_result.md")
        logger.info(f"   分析文件: test_analysis_result.json")
        
    except Exception as e:
        logger.error(f"💥 测试执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
