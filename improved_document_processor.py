#!/usr/bin/env python3
"""
基于阿里云测试结果改进的文档处理器
根据实际API输入输出完善业务逻辑
"""

import asyncio
import json
import logging
import re
import time
from typing import Dict, Any, Optional, List
from pathlib import Path
import uuid

# 模拟导入（实际使用时需要正确的导入路径）
from src.config.settings import Settings
from src.document_rag.ali_service import AlibabaCloudService
from src.services.minio_service import MinioService

logger = logging.getLogger(__name__)


class ImprovedDocumentProcessor:
    """改进的文档处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self.settings = Settings()
        self.ali_service = AlibabaCloudService(self.settings.alibaba_cloud)
        self.minio_service = MinioService(self.settings.minio)
        
        # 处理统计
        self.processing_stats = {
            "total_processed": 0,
            "successful_text_extraction": 0,
            "image_only_documents": 0,
            "failed_processing": 0,
            "average_processing_time": 0
        }
        
    async def analyze_document_content(self, markdown_content: str) -> Dict[str, Any]:
        """
        分析文档内容类型和质量
        
        Args:
            markdown_content: 阿里云解析的markdown内容
            
        Returns:
            Dict[str, Any]: 内容分析结果
        """
        analysis = {
            "content_type": "unknown",
            "quality_score": 0,
            "has_text": False,
            "has_images": False,
            "has_tables": False,
            "text_ratio": 0,
            "image_count": 0,
            "text_length": 0,
            "recommendations": []
        }
        
        if not markdown_content or not markdown_content.strip():
            analysis["content_type"] = "empty"
            analysis["recommendations"].append("文档解析结果为空，可能是不支持的格式或损坏的文件")
            return analysis
            
        # 统计图片数量
        image_pattern = r'!\[.*?\]\(.*?\)'
        images = re.findall(image_pattern, markdown_content)
        analysis["image_count"] = len(images)
        analysis["has_images"] = len(images) > 0
        
        # 移除图片标记后计算纯文本内容
        text_content = re.sub(image_pattern, '', markdown_content)
        text_content = text_content.strip()
        analysis["text_length"] = len(text_content)
        analysis["has_text"] = len(text_content) > 50  # 至少50个字符才算有意义的文本
        
        # 检查表格
        analysis["has_tables"] = '|' in markdown_content and '---' in markdown_content
        
        # 计算文本比例
        total_content_length = len(markdown_content)
        if total_content_length > 0:
            analysis["text_ratio"] = analysis["text_length"] / total_content_length
        
        # 确定内容类型
        if analysis["has_text"] and analysis["has_images"]:
            analysis["content_type"] = "mixed"
            analysis["quality_score"] = 90
        elif analysis["has_text"] and not analysis["has_images"]:
            analysis["content_type"] = "text_only"
            analysis["quality_score"] = 85
        elif not analysis["has_text"] and analysis["has_images"]:
            analysis["content_type"] = "image_only"
            analysis["quality_score"] = 60
            analysis["recommendations"].append("文档只包含图片，可能是扫描版PDF，建议启用OCR功能")
        else:
            analysis["content_type"] = "empty"
            analysis["quality_score"] = 0
            analysis["recommendations"].append("文档内容为空或无法解析")
            
        # 质量评估和建议
        if analysis["text_ratio"] < 0.1 and analysis["has_images"]:
            analysis["recommendations"].append("文本内容较少，主要为图片内容")
            
        if analysis["text_length"] < 100:
            analysis["recommendations"].append("文本内容较短，可能需要人工审核")
            
        if analysis["image_count"] > 10:
            analysis["recommendations"].append("图片数量较多，处理时间可能较长")
            
        return analysis
        
    async def enhanced_process_document(self, 
                                      file_url: str, 
                                      file_name: str,
                                      enable_ocr: bool = True,
                                      enable_formula: bool = True,
                                      enable_llm: bool = True) -> Dict[str, Any]:
        """
        增强的文档处理流程
        
        Args:
            file_url: 文件URL
            file_name: 文件名
            enable_ocr: 是否启用OCR
            enable_formula: 是否启用公式增强
            enable_llm: 是否启用LLM增强
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        start_time = time.time()
        
        result = {
            "success": False,
            "file_name": file_name,
            "processing_time": 0,
            "content_analysis": {},
            "processed_content": "",
            "image_urls": [],
            "error": None,
            "recommendations": []
        }
        
        try:
            logger.info(f"🚀 开始处理文档: {file_name}")
            
            # 1. 提交阿里云解析任务
            logger.info(f"📤 提交阿里云解析任务...")
            submit_result = await self.ali_service.submit_file(
                file_url=file_url,
                file_name=file_name
            )
            
            if not submit_result["success"]:
                result["error"] = f"任务提交失败: {submit_result.get('error')}"
                return result
                
            job_id = submit_result["job_id"]
            logger.info(f"✅ 任务提交成功，Job ID: {job_id}")
            
            # 2. 轮询等待任务完成
            logger.info(f"⏳ 等待任务完成...")
            max_wait_time = 600  # 10分钟超时
            check_interval = 15  # 15秒检查一次
            waited_time = 0
            
            while waited_time < max_wait_time:
                status_result = await self.ali_service.check_job_status(job_id)
                
                if status_result["success"] and status_result["is_completed"]:
                    if status_result["is_success"]:
                        logger.info(f"✅ 任务完成成功")
                        break
                    else:
                        result["error"] = f"任务失败: {status_result.get('message')}"
                        return result
                        
                await asyncio.sleep(check_interval)
                waited_time += check_interval
                
                if waited_time % 60 == 0:  # 每分钟记录一次
                    logger.info(f"⏱️ 已等待 {waited_time//60} 分钟...")
                    
            if waited_time >= max_wait_time:
                result["error"] = f"任务超时，等待时间超过 {max_wait_time//60} 分钟"
                return result
                
            # 3. 获取解析结果
            logger.info(f"📥 获取解析结果...")
            parse_result = await self.ali_service.get_job_result(job_id)
            
            if not parse_result["success"]:
                result["error"] = f"获取解析结果失败: {parse_result.get('error')}"
                return result
                
            raw_content = parse_result["result"]
            logger.info(f"📄 原始内容长度: {len(raw_content)} 字符")
            
            # 4. 分析内容
            logger.info(f"🔍 分析文档内容...")
            content_analysis = await self.analyze_document_content(raw_content)
            result["content_analysis"] = content_analysis
            
            logger.info(f"📊 内容分析结果:")
            logger.info(f"   - 内容类型: {content_analysis['content_type']}")
            logger.info(f"   - 质量评分: {content_analysis['quality_score']}")
            logger.info(f"   - 文本长度: {content_analysis['text_length']}")
            logger.info(f"   - 图片数量: {content_analysis['image_count']}")
            
            # 5. 处理图片（如果有）
            processed_content = raw_content
            if content_analysis["has_images"]:
                logger.info(f"🖼️ 处理文档图片...")
                
                try:
                    processed_content = await self.ali_service.download_images_from_markdown(
                        raw_content, 
                        self.minio_service, 
                        file_name, 
                        self.settings.document_api.BUCKET_NAME
                    )
                    
                    # 提取处理后的图片URL
                    image_pattern = r'!\[.*?\]\((.*?)\)'
                    result["image_urls"] = re.findall(image_pattern, processed_content)
                    
                    logger.info(f"✅ 图片处理完成，共 {len(result['image_urls'])} 张图片")
                    
                except Exception as e:
                    logger.warning(f"⚠️ 图片处理失败: {e}")
                    result["recommendations"].append("图片处理失败，使用原始图片链接")
                    
            result["processed_content"] = processed_content
            
            # 6. 生成建议
            recommendations = content_analysis.get("recommendations", [])
            
            if content_analysis["content_type"] == "image_only":
                recommendations.append("建议检查原始文档是否为扫描版，考虑使用更高级的OCR服务")
                
            if content_analysis["quality_score"] < 70:
                recommendations.append("文档质量较低，建议人工审核处理结果")
                
            if waited_time > 120:  # 超过2分钟
                recommendations.append("处理时间较长，建议优化文档大小或格式")
                
            result["recommendations"] = recommendations
            
            # 7. 更新统计信息
            self.processing_stats["total_processed"] += 1
            
            if content_analysis["has_text"]:
                self.processing_stats["successful_text_extraction"] += 1
            elif content_analysis["content_type"] == "image_only":
                self.processing_stats["image_only_documents"] += 1
                
            processing_time = time.time() - start_time
            result["processing_time"] = processing_time
            
            # 更新平均处理时间
            total = self.processing_stats["total_processed"]
            current_avg = self.processing_stats["average_processing_time"]
            self.processing_stats["average_processing_time"] = (current_avg * (total - 1) + processing_time) / total
            
            result["success"] = True
            logger.info(f"🎉 文档处理完成，耗时: {processing_time:.2f}s")
            
        except Exception as e:
            self.processing_stats["failed_processing"] += 1
            result["error"] = f"处理异常: {str(e)}"
            logger.error(f"❌ 文档处理失败: {e}")
            
        finally:
            result["processing_time"] = time.time() - start_time
            
        return result
        
    def get_processing_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        stats = self.processing_stats.copy()
        
        if stats["total_processed"] > 0:
            stats["success_rate"] = (stats["successful_text_extraction"] + stats["image_only_documents"]) / stats["total_processed"]
            stats["text_extraction_rate"] = stats["successful_text_extraction"] / stats["total_processed"]
            stats["image_only_rate"] = stats["image_only_documents"] / stats["total_processed"]
            stats["failure_rate"] = stats["failed_processing"] / stats["total_processed"]
        else:
            stats["success_rate"] = 0
            stats["text_extraction_rate"] = 0
            stats["image_only_rate"] = 0
            stats["failure_rate"] = 0
            
        return stats
        
    async def batch_process_documents(self, documents: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        批量处理文档
        
        Args:
            documents: 文档列表，每个元素包含 file_url 和 file_name
            
        Returns:
            Dict[str, Any]: 批量处理结果
        """
        batch_result = {
            "total_documents": len(documents),
            "successful_documents": 0,
            "failed_documents": 0,
            "results": [],
            "batch_statistics": {}
        }
        
        logger.info(f"🚀 开始批量处理 {len(documents)} 个文档")
        
        for i, doc in enumerate(documents, 1):
            logger.info(f"📋 处理进度: {i}/{len(documents)} - {doc['file_name']}")
            
            result = await self.enhanced_process_document(
                file_url=doc["file_url"],
                file_name=doc["file_name"]
            )
            
            batch_result["results"].append(result)
            
            if result["success"]:
                batch_result["successful_documents"] += 1
            else:
                batch_result["failed_documents"] += 1
                
        batch_result["batch_statistics"] = self.get_processing_statistics()
        
        logger.info(f"📊 批量处理完成:")
        logger.info(f"   - 成功: {batch_result['successful_documents']}")
        logger.info(f"   - 失败: {batch_result['failed_documents']}")
        logger.info(f"   - 成功率: {batch_result['successful_documents']/len(documents)*100:.1f}%")
        
        return batch_result


async def test_improved_processor():
    """测试改进的文档处理器"""
    print("🧪 测试改进的文档处理器")
    print("=" * 50)
    
    processor = ImprovedDocumentProcessor()
    
    # 使用之前上传的测试文件
    test_url = "http://1.15.148.48:9000/zhanshu/simple_test/20240429103255110730.pdf"
    test_name = "20240429103255110730.pdf"
    
    result = await processor.enhanced_process_document(test_url, test_name)
    
    print("📊 处理结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    print("\n📈 处理统计:")
    stats = processor.get_processing_statistics()
    print(json.dumps(stats, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    asyncio.run(test_improved_processor())
